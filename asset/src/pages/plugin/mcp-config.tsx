import { useCurrentSpace } from '@/components/space-provider';
import { ModalForm } from '@topthink/common';

export default function McpConfig({ plugin, onSuccess }: { plugin: Plugin, onSuccess?: () => void }) {
    const { current } = useCurrentSpace();
    const { config = {} } = plugin;

    return <ModalForm
        text={'配置'}
        action={`/space/${current.hash_id}/plugin/${plugin.hash_id}/config`}
        modalProps={{
            header: 'MCP配置',
        }}
        formData={config}
        schema={{
            type: 'object',
            properties: {
                url: {
                    type: 'string',
                    title: 'MCP地址'
                },
                transport: {
                    type: 'string',
                    title: '传输方式',
                    enum: ['sse'],
                    enumNames: ['SSE'],
                    default: 'sse'
                }
            }
        }}
        onSuccess={onSuccess}
    />;
}
