import { RemoveIcon } from '@/components/action';
import { ModalButton, Panel, Tooltip } from '@topthink/common';
import isEqual from 'lodash/isEqual';
import { Form, Row } from 'react-bootstrap';
import { PanelItem } from '../components';
import usePlugins from '../use-plugins';
import Modal from './modal';
import Setting from './setting';

export default function Plugin({ value, onChange }: BotAgentConfigurationUpdaterProps<'tools'>) {
    const { plugins } = usePlugins({
        tools: value,
        onSuccess(plugins) {
            onChange(draft => {
                const newDraft = draft.filter(item => {
                    const plugin = plugins.find(p => p.name === item.plugin);
                    return plugin?.tools.find(t => t.name === item.name);
                });
                if (!isEqual(draft, newDraft)) {
                    return newDraft;
                }
            });
        }
    });

    // 找出未查询到的插件工具
    const foundTools = new Set();
    plugins.forEach(plugin => {
        plugin.tools.forEach(tool => {
            foundTools.add(`${plugin.name}:${tool.name}`);
        });
    });

    const missingTools = value.filter(tool => {
        return !foundTools.has(`${tool.plugin}:${tool.name}`);
    });

    const action = <ModalButton
        size='sm'
        variant='light'
        modalProps={{ footer: false, size: 'lg', header: '选择插件' }}
        text={<><i className='bi bi-plus me-1' />添加</>}
    >
        <Modal value={value} onChange={onChange} />
    </ModalButton>;

    return <Panel title='插件' action={action}>
        {(plugins.length > 0 || missingTools.length > 0) ?
            <Row className='g-2'>
                {/* 显示正常的插件工具 */}
                {plugins.map((plugin) => {
                    return plugin.tools.map((tool) => {
                        const t = value.find(t => t.plugin === plugin.name && t.name === tool.name);
                        if (!t) {
                            return null;
                        }
                        return <PanelItem key={tool.name}>
                            <img className='me-2 rounded' src={plugin.icon} width={16} height={16} />
                            <span className='me-auto'>[{plugin.title}] {tool.title}</span>
                            <RemoveIcon className='ms-1' onClick={() => {
                                onChange(draft => {
                                    const index = draft.findIndex(t => t.plugin === plugin.name && t.name === tool.name);
                                    if (index !== -1) draft.splice(index, 1);
                                });
                            }} />
                            <Setting
                                tool={tool}
                                value={t.args}
                                onChange={(value) => {
                                    onChange(draft => {
                                        const i = draft.find(t => t.plugin === plugin.name && t.name === tool.name);
                                        if (i) {
                                            i.args = value;
                                        }
                                    });
                                }}
                            />
                            <Form.Check
                                className='ms-2'
                                type='switch'
                                checked={t.enable === undefined || t.enable}
                                onChange={(e) => onChange(draft => {
                                    const i = draft.find(t => t.plugin === plugin.name && t.name === tool.name);
                                    if (i) {
                                        i.enable = e.target.checked;
                                    }
                                })}
                            />
                        </PanelItem>;
                    });
                })}
                {/* 显示未查询到的插件工具，标红 */}
                {missingTools.map((tool) => (
                    <PanelItem key={`${tool.plugin}:${tool.name}`}>
                        <Tooltip tooltip={'该插件工具未找到或已被删除'}>
                            <i className={'bi bi-info-circle text-danger me-2'} />
                        </Tooltip>
                        <span className='me-auto text-danger'>
                            [{tool.plugin}] {tool.name}
                        </span>
                        <RemoveIcon onClick={() => {
                            onChange(draft => {
                                const index = draft.findIndex(t => t.plugin === tool.plugin && t.name === tool.name);
                                if (index !== -1) draft.splice(index, 1);
                            });
                        }} />
                    </PanelItem>
                ))}
            </Row> :
            <div className='text-muted'>
                插件能够让智能体调用外部API，例如搜索信息、浏览网页、生成图片等，扩展智能体的能力和使用场景。
            </div>
        }
    </Panel>;
}
