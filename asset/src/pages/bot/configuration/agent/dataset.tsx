import { Row, Stack } from 'react-bootstrap';
import { Button, ModalButton, Panel, Table } from '@topthink/common';
import { useBot } from '@/pages/bot/provider';
import byteSize from 'byte-size';
import { PanelItem } from './components';
import { RemoveIcon } from '@/components/action';
import useDatasets from '@/hooks/use-datasets';

export default function Dataset({ value, onChange }: BotAgentConfigurationUpdaterProps<'dataset'>) {
    const { space } = useBot();

    const datasets = useDatasets(value.datasets);

    const action = <ModalButton
        size='sm'
        variant='light'
        modalProps={{ footer: false, size: 'lg', header: '选择知识库' }}
        text={<><i className='bi bi-plus me-1' />添加</>}
    >
        <Table
            card={false}
            toolBarRender={false}
            source={`/space/${space.hash_id}/dataset`}
            columns={[
                {
                    dataIndex: 'name',
                    title: '名称',
                    render({ record }) {
                        return <Stack gap={1}>
                            {record.name}
                            <span className='fs-7 text-muted'>{record.description || '暂无描述'}</span>
                        </Stack>;
                    }
                },
                {
                    dataIndex: 'size',
                    width: 100,
                    title: '文件大小',
                    align: 'center',
                    render({ value }) {
                        return `${byteSize(value)}`;
                    }
                },
                {
                    dataIndex: 'create_time',
                    title: '创建时间',
                    width: 150,
                },
                {
                    key: 'action',
                    title: '操作',
                    width: 120,
                    align: 'right',
                    render({ record }) {
                        if (value.datasets.includes(record.hash_id)) {
                            return <Button
                                onClick={() => {
                                    onChange(draft => {
                                        const index = draft.datasets.indexOf(record.hash_id);
                                        if (index !== -1) draft.datasets.splice(index, 1);
                                    });
                                }}
                                className={'link-danger'}
                            >移除</Button>;
                        } else {
                            return <Button
                                onClick={() => {
                                    onChange(draft => {
                                        draft.datasets.push(record.hash_id);
                                    });
                                }}
                            >添加</Button>;
                        }
                    }
                }
            ]} />
    </ModalButton>;

    return <Panel title='知识库' action={action}>
        {datasets.length > 0 ?
            <Row className='g-2'>
                {datasets.map((dataset) => (
                    <PanelItem key={dataset.hash_id}>
                        <i className='bi bi-journal-text text-muted me-2' />
                        <span className='me-auto'>{dataset.name}</span>
                        <RemoveIcon onClick={() => {
                            onChange(draft => {
                                const index = draft.datasets.indexOf(dataset.hash_id);
                                if (index !== -1) draft.datasets.splice(index, 1);
                            });
                        }} />
                    </PanelItem>
                ))}
            </Row> :
            <div className='text-muted'>添加知识库后，用户发送消息时，智能体能够引用知识库中的内容回答用户问题。</div>
        }
    </Panel>;
}
