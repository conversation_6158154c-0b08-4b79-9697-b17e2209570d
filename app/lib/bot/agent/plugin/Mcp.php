<?php

namespace app\lib\bot\agent\plugin;

use app\lib\mcp\ServerEvent;
use think\agent\Plugin;
use think\agent\tool\Args;
use think\agent\tool\FunctionCall;
use think\agent\tool\result\Raw;
use think\facade\Cache;
use think\helper\Arr;

class Mcp extends Plugin
{
    protected $client;

    protected $tools;

    public function __construct(protected $url, protected $transport = 'sse')
    {
    }

    public function getTools()
    {
        if (is_null($this->tools)) {
            $this->tools = $this->buildTools();
        }
        return $this->tools;
    }

    protected function buildTools()
    {
        if (empty($this->url)) {
            return [];
        }

        $tools = Cache::remember("mcp-tools-{$this->transport}:{$this->url}", function () {
            return $this->getClient()->listTools();
        }, 60 * 10);

        return array_map(function ($tool) {
            return new class(fn() => $this->getClient(), $tool) extends FunctionCall {

                public function __construct(protected $clientResolver, $tool)
                {
                    $this->name        = $tool['name'];
                    $this->title       = $tool['name'];
                    $this->description = $tool['description'];
                    $this->parameters  = $tool['inputSchema'];
                }

                public function getParameters()
                {
                    $required   = Arr::get($this->parameters, 'required', []);
                    $parameters = Arr::get($this->parameters, 'properties', []);
                    foreach ($required as $key) {
                        if (isset($parameters[$key])) {
                            $parameters[$key]['required'] = true;
                        }
                    }
                    return $parameters;
                }

                public function getFee()
                {
                    return 0;
                }

                public function getLlmDescription()
                {
                    return $this->description;
                }

                public function getLlmParameters()
                {
                    return $this->parameters;
                }

                protected function getClient()
                {
                    return call_user_func($this->clientResolver);
                }

                protected function run(Args $args)
                {
                    $result = $this->getClient()->callTool($this->name, [...$args]);

                    return new Raw([
                        'response' => json_encode(Arr::get($result, 'content'), JSON_UNESCAPED_UNICODE),
                        'error'    => Arr::get($result, 'isError', false),
                    ]);
                }
            };
        }, $tools);
    }

    protected function getClient()
    {
        if (empty($this->client)) {
            $this->client = ServerEvent::connect($this->url);
        }
        return $this->client;
    }

    public function __destruct()
    {
        if (!empty($this->client)) {
            $this->client->close();
            $this->client = null;
        }
    }
}
