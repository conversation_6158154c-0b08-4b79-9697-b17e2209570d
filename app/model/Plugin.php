<?php

namespace app\model;

use app\Exception;
use app\lib\bot\agent\plugin\Mcp;
use app\lib\Hashids;
use Nette\Utils\Finder;
use think\agent\OpenApi;
use think\ai\Client;
use think\helper\Arr;
use think\helper\Str;
use think\Model;
use Throwable;

/**
 * Class app\model\Plugin
 *
 * @property \app\lib\Date $create_time
 * @property \app\lib\Date $update_time
 * @property array $config
 * @property int $id
 * @property int $space_id
 * @property int $type
 * @property int $sort
 * @property int $status
 * @property string $auth
 * @property string $description
 * @property string $icon
 * @property string $name
 * @property string $schema
 * @property string $title
 * @property-read mixed $credentials
 * @property-read mixed $hash_id
 * @property-read mixed $tools
 */
class Plugin extends Model
{
    const TYPE_THINK   = 1;
    const TYPE_BUILTIN = 2;
    const TYPE_API     = 3;
    const TYPE_MCP     = 4;

    protected $append = ['hash_id'];

    protected $hidden = ['auth', 'schema'];

    protected $type = [
        'config' => 'json',
        'auth'   => 'json',
    ];

    public static function sync($force = false)
    {
        try {
            self::syncBuiltin();
            self::syncThink();
        } catch (Throwable $e) {
            if ($force) {
                throw $e;
            }
        }
    }

    public static function syncBuiltin()
    {
        //同步内置插件
        $plugins = Plugin::where('type', self::TYPE_BUILTIN)->select()->column(null, 'name');

        $dirs = Finder::findDirectories()
            ->in(app_path('lib/bot/agent/plugin'))
            ->collect();

        foreach ($dirs as $info) {
            $name    = $info->getBasename();
            $builtin = self::resolveBuiltin($name);

            $data = [
                'type'        => self::TYPE_BUILTIN,
                'name'        => $builtin->getName(),
                'title'       => $builtin->getTitle(),
                'description' => $builtin->getDescription(),
                'config'      => [
                    'tools'       => $builtin->getTools(),
                    'credentials' => $builtin->getCredentials(),
                ],
            ];

            if (isset($plugins[$builtin->getName()])) {
                try {
                    $plugins[$builtin->getName()]->save($data);
                } finally {
                    unset($plugins[$builtin->getName()]);
                }
            } else {
                Plugin::create($data);
            }
        }

        foreach ($plugins as $plugin) {
            $plugin->delete();
        }
    }

    public static function syncThink()
    {
        //同步ThinkAI插件
        $plugins = Plugin::where('type', self::TYPE_THINK)->select()->column(null, 'name');

        foreach (app(Client::class)->plugin()->list() as $info) {
            $data = [
                'type'        => self::TYPE_THINK,
                'name'        => $info['name'],
                'title'       => $info['title'],
                'description' => $info['description'],
                'icon'        => $info['icon'],
                'config'      => [
                    'tools' => $info['tools'],
                ],
            ];

            $name = "plugin-{$info['name']}";

            if (isset($plugins[$name])) {
                try {
                    $plugins[$name]->save($data);
                } finally {
                    unset($plugins[$name]);
                }
            } else {
                Plugin::create($data);
            }
        }

        foreach ($plugins as $plugin) {
            $plugin->delete();
        }
    }

    /**
     * @param $name
     * @return \think\agent\Plugin|null
     */
    public static function resolve(Space $space, $name)
    {
        if (str_starts_with($name, 'custom-')) {
            //自定义
            $id = Hashids::decode(substr($name, 7));

            /** @var \app\model\Plugin $plugin */
            $plugin = $space->plugins()->where('id', $id)->findOrFail();

            return $plugin->getDriver();
        } else {
            return self::resolveBuiltin($name);
        }
    }

    /**
     * @param $name
     * @return \think\agent\Plugin|null
     */
    public static function resolveBuiltin($name)
    {
        $class = "\\app\\lib\\bot\\agent\\plugin\\{$name}\\" . Str::studly($name);

        if (class_exists($class)) {
            return new $class();
        }
    }

    public function getDriver()
    {
        $type   = $this->getAttr('type');
        $config = $this->getAttr('config');

        return match ($type) {
            self::TYPE_MCP => new Mcp($config['url'] ?? null, $config['transport'] ?? null),
            self::TYPE_API => new OpenApi($config['schema'] ?? null, $config['auth'] ?? null),
            default => throw new Exception('unknown plugin type')
        };
    }

    protected function isBuiltin()
    {
        return $this->getAttr('type') === self::TYPE_BUILTIN;
    }

    protected function isCustom()
    {
        return $this->getAttr('type') === self::TYPE_API || $this->getAttr('type') === self::TYPE_MCP;
    }

    protected function getHashIdAttr()
    {
        return Hashids::encode($this->id);
    }

    protected function getNameAttr($name)
    {
        $type = $this->getAttr('type');

        return match ($type) {
            self::TYPE_THINK => "plugin-{$name}",
            self::TYPE_BUILTIN => $name,
            self::TYPE_API, self::TYPE_MCP => "custom-{$this->hash_id}",
            default => $name
        };
    }

    protected function getToolsAttr()
    {
        if ($this->isCustom()) {
            try {
                $driver = $this->getDriver();
                return $driver->getTools();
            } catch (Throwable) {
                return [];
            }
        } else {
            return Arr::get($this->getAttr('config'), 'tools', []);
        }
    }

    protected function getCredentialsAttr()
    {
        if ($this->isBuiltin()) {
            return Arr::get($this->getAttr('config'), 'credentials');
        } else {
            return null;
        }
    }

    protected function getIconAttr($icon)
    {
        if ($this->isBuiltin()) {
            $name = $this->getAttr('name');
            $file = app_path("lib/bot/agent/plugin/{$name}") . 'icon.png';

            if (file_exists($file)) {
                $content = base64_encode(file_get_contents($file));
                $icon    = "data:image/png;base64,{$content}";
            }
        }

        if (empty($icon)) {
            $file    = app_path('lib/bot/agent/plugin') . 'icon.png';
            $content = base64_encode(file_get_contents($file));
            $icon    = "data:image/png;base64,{$content}";
        }

        return $icon;
    }
}
